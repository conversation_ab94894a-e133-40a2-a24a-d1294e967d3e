#!/usr/bin/env python3
import json
import sys

class TestBot:
    def __init__(self):
        print("Creating TestBot...")
        self.logger = None
        print("Loading config...")
        self.config = self.load_config()
        print("Config loaded successfully!")
        
    def load_config(self):
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"Config: {config}")
            return config
        except Exception as e:
            print(f"Error loading config: {e}")
            sys.exit(1)

if __name__ == "__main__":
    try:
        bot = TestBot()
        print("Bot created successfully!")
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
