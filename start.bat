@echo off
echo 🚀 Starting Telegram Userbot with Docker...

REM إنشاء مجلد البيانات إذا لم يكن موجوداً
if not exist "data" mkdir data

REM التحقق من وجود Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not installed!
    echo 📦 Please install Docker Desktop first: https://docs.docker.com/desktop/windows/
    pause
    exit /b 1
)

REM التحقق من وجود Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose is not installed!
    echo 📦 Please install Docker Compose first
    pause
    exit /b 1
)

REM بناء وتشغيل البوت
echo 🔨 Building Docker image...
docker-compose build

echo 🚀 Starting userbot container...
docker-compose up -d

echo ✅ Userbot started successfully!
echo 📊 To view logs: docker-compose logs -f
echo 🛑 To stop: docker-compose down

REM عرض السجلات
echo 📋 Recent logs:
timeout /t 3 /nobreak >nul
docker-compose logs --tail=20

pause
