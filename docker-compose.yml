version: '3.8'

services:
  telegram-userbot:
    build: .
    container_name: telegram_userbot
    restart: unless-stopped
    
    # ربط المجلدات لحفظ الجلسة والسجلات
    volumes:
      - ./data:/app/data
      - ./config.json:/app/config.json:ro
      - ./userbot.log:/app/userbot.log
      - ./userbot_session.session:/app/userbot_session.session
    
    # متغيرات البيئة (اختيارية)
    environment:
      - PYTHONUNBUFFERED=1
      - TZ=Asia/Baghdad
    
    # إعادة التشغيل التلقائي
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    
    # للمراقبة (اختياري)
    healthcheck:
      test: ["CMD", "python", "-c", "import os; exit(0 if os.path.exists('/app/userbot_session.session') else 1)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# إنشاء شبكة مخصصة (اختياري)
networks:
  default:
    name: telegram_userbot_network
