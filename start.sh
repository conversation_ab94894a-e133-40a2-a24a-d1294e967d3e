#!/bin/bash

echo "🚀 Starting Telegram Userbot with Docker..."

# إنشاء مجلد البيانات إذا لم يكن موجوداً
mkdir -p data

# التحقق من وجود Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed!"
    echo "📦 Please install Docker first: https://docs.docker.com/get-docker/"
    exit 1
fi

# التحقق من وجود Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed!"
    echo "📦 Please install Docker Compose first"
    exit 1
fi

# بناء وتشغيل البوت
echo "🔨 Building Docker image..."
docker-compose build

echo "🚀 Starting userbot container..."
docker-compose up -d

echo "✅ Userbot started successfully!"
echo "📊 To view logs: docker-compose logs -f"
echo "🛑 To stop: docker-compose down"

# عرض السجلات
echo "📋 Recent logs:"
sleep 3
docker-compose logs --tail=20
