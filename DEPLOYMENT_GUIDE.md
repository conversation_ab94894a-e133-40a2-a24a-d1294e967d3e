# 🚀 دليل النشر السريع - Telegram Userbot

## 📋 الخطوات السريعة للنشر على Hugging Face Spaces

### 1. الحصول على بيانات Telegram API

1. اذهب إلى [my.telegram.org](https://my.telegram.org)
2. سجل دخول برقم هاتفك
3. اذهب إلى "API Development Tools"
4. أنشئ تطبيق جديد
5. احفظ `API_ID` و `API_HASH`

### 2. إنشاء Space جديد

1. اذهب إلى [Hugging Face Spaces](https://huggingface.co/spaces)
2. انقر على "Create new Space"
3. اختر:
   - **SDK**: Gradio
   - **Hardware**: CPU basic (مجاني)
   - **Visibility**: Private (مُوصى به للأمان)

### 3. رفع الملفات

ارفع هذه الملفات إلى Space الخاص بك:
```
├── app.py
├── main.py
├── verification_handler.py
├── config.json
├── requirements.txt
├── Dockerfile
└── README.md
```

### 4. تكوين متغيرات البيئة

في إعدادات Space، أضف هذه **Secrets**:

```
API_ID=your_api_id_here
API_HASH=your_api_hash_here
PHONE_NUMBER=+1234567890
```

⚠️ **مهم**: تأكد من إضافة رمز الدولة لرقم الهاتف

### 5. التحقق الأول

1. ابدأ تشغيل Space
2. اذهب إلى تبويب **Verification**
3. سيرسل البوت كود تحقق لهاتفك تلقائياً
4. أدخل الكود المكون من 5 أرقام
5. انقر "Submit Code"

### 6. مراقبة البوت

- **Status Tab**: لمراقبة السجلات والنشاط
- **Configuration Tab**: لعرض الإعدادات الحالية
- **Help Tab**: للمساعدة والتوجيهات

## 🔧 إعدادات متقدمة

### تخصيص الردود

عدّل `config.json`:

```json
{
  "smart_replies": [
    "ردك المخصص الأول",
    "ردك المخصص الثاني"
  ],
  "inactivity_minutes": 5,
  "rate_limit_seconds": 60
}
```

### إضافة أوامر مخصصة

```json
{
  "bot_commands": {
    "🎯 أمر مخصص": "وصف الأمر"
  },
  "command_responses": {
    "🎯 أمر مخصص": "الرد المخصص للأمر"
  }
}
```

### استبعاد مستخدمين

```json
{
  "excluded_users": [123456789, 987654321]
}
```

## 🛠️ استكشاف الأخطاء

### مشكلة التحقق
- تأكد من رقم الهاتف يتضمن رمز الدولة
- كود التحقق ينتهي خلال 30 دقيقة
- راجع تبويب Status للأخطاء

### البوت لا يرد
- تأكد من أنك "غير نشط" (لم ترسل رسائل مؤخراً)
- تحقق من انتهاء مدة عدم النشاط
- راجع قائمة المستخدمين المستبعدين

### مشاكل الجلسة
- الجلسات محفوظة تلقائياً
- أعد تشغيل Space لاستعادة النسخة الاحتياطية
- أعد التحقق إذا أصبحت الجلسة غير صالحة

## 📱 الميزات المتاحة

### ✅ ما يعمل
- ردود تلقائية ذكية ومتنوعة
- نظام أوامر بعد 3 رسائل
- ردود مخصصة للأوامر
- كشف عدم النشاط
- حفظ الجلسة
- واجهة ويب للمراقبة

### ⚠️ القيود
- يعمل فقط مع الرسائل الخاصة
- لا يرد على البوتات
- يحتاج تحقق أولي عبر الواجهة
- محدود بقيود Telegram API

## 🔐 الأمان

- احتفظ بـ Space كـ Private
- لا تشارك بيانات API مع أحد
- الجلسات مشفرة ومحفوظة بأمان
- البيانات الحساسة مخفية في السجلات

## 📞 الدعم

إذا واجهت مشاكل:
1. راجع تبويب Status للأخطاء
2. تأكد من صحة متغيرات البيئة
3. أعد تشغيل Space
4. راجع هذا الدليل مرة أخرى

---

**ملاحظة**: هذا البوت للاستخدام الشخصي فقط. يرجى احترام شروط خدمة Telegram واستخدامه بمسؤولية.
