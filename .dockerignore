# Git files
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md
docs/

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/
.env

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Session files (will be mounted as volume)
*.session
*.session-journal

# Log files (will be mounted as volume)
*.log

# Test files
test_*.py
*_test.py
tests/

# Docker files
Dockerfile
docker-compose.yml
.dockerignore

# Temporary files
tmp/
temp/
*.tmp

# Backup files
*.bak
*.backup
