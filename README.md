---
title: Telegram Userbot
emoji: 🤖
colorFrom: blue
colorTo: purple
sdk: gradio
sdk_version: 4.0.0
app_file: app.py
pinned: false
license: mit
---

# 🤖 Telegram Userbot

A smart Telegram userbot that automatically replies to private messages when you're inactive.

## ✨ Features

- 🔄 Smart auto-replies with different messages each time
- ⏰ Only replies when you're actually inactive
- 🎯 Command system after 3+ messages
- 📝 Custom responses for specific commands
- 🚫 User exclusion list
- ⚡ Rate limiting to prevent spam

## 🚀 Setup

### 1. Get Telegram API Credentials

1. Go to [my.telegram.org](https://my.telegram.org)
2. Create a new application
3. Get your `API_ID` and `API_HASH`

### 2. Configure Environment Variables

Add these secrets in your Space settings:

```
API_ID=your_api_id
API_HASH=your_api_hash
PHONE_NUMBER=+**********
```

### 3. First Time Verification

1. Start the Space
2. Go to the **Verification** tab
3. Enter the verification code sent to your phone
4. The bot will start working automatically

## 📱 How It Works

- Monitors your activity on Telegram
- Sends auto-replies only when you're inactive
- Uses different reply messages to seem natural
- Shows command options after 3 messages from the same user

## ⚙️ Configuration

Edit `config.json` to customize:
- Inactivity timeout (default: 5 minutes)
- Custom reply messages
- Rate limiting settings
- Excluded users

## 🔐 Security

- Keep your Space private
- API credentials are securely stored
- Session files are encrypted
- No sensitive data in logs

## 📊 Monitoring

Use the web interface to:
- Monitor bot activity and logs
- Check verification status
- View current configuration
- Get help and documentation

---

**Note**: This bot is for personal use only. Please respect Telegram's Terms of Service.
