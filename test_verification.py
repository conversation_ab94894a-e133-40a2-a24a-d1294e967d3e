#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for verification handler
"""

import json
import os
import sys
from pathlib import Path

def test_config_loading():
    """Test configuration loading"""
    print("🧪 Testing configuration loading...")
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        required_fields = ['api_id', 'api_hash', 'phone_number']
        for field in required_fields:
            if field in config:
                print(f"  ✅ {field}: Found")
            else:
                print(f"  ❌ {field}: Missing")
        
        print(f"  ✅ Smart replies: {len(config.get('smart_replies', []))} configured")
        print(f"  ✅ Commands: {len(config.get('bot_commands', {}))} configured")
        
    except Exception as e:
        print(f"  ❌ Error loading config: {e}")

def test_verification_handler():
    """Test verification handler"""
    print("\n🧪 Testing verification handler...")
    
    try:
        from verification_handler import VerificationHandler
        
        # Create dummy config
        config = {
            'api_id': 'test',
            'api_hash': 'test',
            'phone_number': '+1234567890',
            'session_name': 'test_session'
        }
        
        handler = VerificationHandler(config)
        print("  ✅ VerificationHandler created successfully")
        
        # Test status methods
        status = handler.get_verification_status()
        print(f"  ✅ Verification status: {status}")
        
    except Exception as e:
        print(f"  ❌ Error testing verification handler: {e}")

def test_main_module():
    """Test main module import"""
    print("\n🧪 Testing main module...")
    
    try:
        # Temporarily set dummy config values
        original_config = None
        config_path = Path('config.json')
        
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                original_config = json.load(f)
            
            # Create test config
            test_config = original_config.copy()
            test_config['api_id'] = '123456'
            test_config['api_hash'] = 'test_hash'
            test_config['phone_number'] = '+1234567890'
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(test_config, f, ensure_ascii=False, indent=4)
        
        # Test import
        import main
        print("  ✅ Main module imported successfully")
        
        # Test userbot creation (without running)
        userbot = main.TelegramUserbot()
        print("  ✅ TelegramUserbot created successfully")
        print(f"  ✅ Smart replies: {len(userbot.get_smart_replies())}")
        print(f"  ✅ Commands list generated: {len(userbot.get_commands_list())} chars")
        
        # Restore original config
        if original_config:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(original_config, f, ensure_ascii=False, indent=4)
        
    except Exception as e:
        print(f"  ❌ Error testing main module: {e}")
        
        # Restore original config on error
        if original_config:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(original_config, f, ensure_ascii=False, indent=4)

def test_app_module():
    """Test app module"""
    print("\n🧪 Testing app module...")
    
    try:
        import app
        print("  ✅ App module imported successfully")
        
        # Test environment setup
        app.setup_environment()
        print("  ✅ Environment setup completed")
        
    except Exception as e:
        print(f"  ❌ Error testing app module: {e}")

def test_file_structure():
    """Test file structure"""
    print("\n🧪 Testing file structure...")
    
    required_files = [
        'main.py',
        'app.py', 
        'verification_handler.py',
        'config.json',
        'requirements.txt',
        'Dockerfile',
        'README.md'
    ]
    
    for file_name in required_files:
        if Path(file_name).exists():
            print(f"  ✅ {file_name}: Found")
        else:
            print(f"  ❌ {file_name}: Missing")

def main():
    """Run all tests"""
    print("🚀 Running Telegram Userbot Tests")
    print("=" * 50)
    
    test_file_structure()
    test_config_loading()
    test_verification_handler()
    test_main_module()
    test_app_module()
    
    print("\n" + "=" * 50)
    print("✅ All tests completed!")
    print("\n📝 Next steps:")
    print("1. Configure your API credentials in Hugging Face Spaces secrets")
    print("2. Deploy to Hugging Face Spaces")
    print("3. Use the verification tab to enter your phone verification code")
    print("4. Monitor the bot through the web interface")

if __name__ == "__main__":
    main()
