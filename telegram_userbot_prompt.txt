🤖 TELEGRAM USERBOT - AUTO REPLY WHEN INACTIVE
==============================================

📋 PROJECT DESCRIPTION:
Create a personal Telegram userbot using Python that automatically replies to private messages ONLY when the user is inactive. The bot should monitor user activity and respond intelligently based on the user's last activity timestamp.

🎯 CORE FUNCTIONALITY:
1. Monitor all incoming private messages
2. Track user activity (outgoing messages as activity indicator)
3. Auto-reply only when user has been inactive for X minutes
4. Prevent duplicate replies to the same person within the cooldown period
5. Work silently in background without interfering with normal usage

🔧 TECHNICAL REQUIREMENTS:

LIBRARY CHOICE:
- Use Telethon or Pyrogram for Telegram API interaction
- Python 3.8+ compatibility
- Async/await pattern for efficient operation

CORE FEATURES TO IMPLEMENT:
1. Session Management:
   - Secure session handling with .session files
   - API credentials management (api_id, api_hash)
   - Phone number authentication

2. Activity Tracking:
   - Monitor outgoing messages as "user activity"
   - Store last activity timestamp
   - Configurable inactivity threshold (default: 5 minutes)

3. Auto-Reply Logic:
   - Check if sender is in private chat (not groups/channels)
   - Verify user has been inactive for X minutes
   - Send auto-reply message only once per person per inactivity period
   - Reset reply status when user becomes active again

4. Configuration System:
   - External config file (JSON/YAML) for easy customization
   - Configurable parameters:
     * Inactivity timeout (minutes)
     * Auto-reply message text
     * Excluded user IDs (don't reply to specific contacts)
     * Enable/disable logging

5. Safety Features:
   - Rate limiting to prevent spam
   - Graceful error handling
   - Clean shutdown mechanism
   - No replies to bots or service messages

📁 FILE STRUCTURE:
```
telegram_userbot/
├── main.py              # Main bot script
├── config.json          # Configuration file
├── requirements.txt     # Dependencies
└── README.md           # Usage instructions
```

⚙️ CONFIGURATION EXAMPLE (config.json):
```json
{
    "api_id": "YOUR_API_ID",
    "api_hash": "YOUR_API_HASH",
    "phone_number": "YOUR_PHONE_NUMBER",
    "inactivity_minutes": 5,
    "auto_reply_message": "مرحباً! أنا غير متاح حالياً، سأرد عليك في أقرب وقت ممكن. 🙏",
    "excluded_users": [],
    "enable_logging": true,
    "session_name": "userbot_session"
}
```

🔨 IMPLEMENTATION REQUIREMENTS:

MAIN SCRIPT STRUCTURE:
1. Import necessary libraries (telethon/pyrogram, asyncio, json, datetime)
2. Load configuration from JSON file
3. Initialize Telegram client with session
4. Create activity tracker class/functions
5. Implement message handlers:
   - Outgoing message handler (update last activity)
   - Incoming message handler (check inactivity and auto-reply)
6. Main async loop with proper error handling

KEY FUNCTIONS TO IMPLEMENT:
- load_config(): Load settings from config.json
- is_user_inactive(): Check if user has been inactive for X minutes
- should_auto_reply(): Determine if auto-reply should be sent
- send_auto_reply(): Send the configured auto-reply message
- update_last_activity(): Update activity timestamp
- main(): Main async function to run the bot

ACTIVITY TRACKING LOGIC:
- Store last_activity_time as datetime object
- Update on every outgoing message
- Check time difference on incoming messages
- Maintain replied_users set with timestamps for cooldown

ERROR HANDLING:
- Network connection issues
- API rate limits
- Invalid configuration
- Session authentication problems
- Graceful shutdown on interruption

🛡️ SECURITY CONSIDERATIONS:
- Never log sensitive information (phone, API keys)
- Use secure session storage
- Implement proper exception handling
- Add rate limiting to prevent abuse
- Validate all configuration inputs

📝 LOGGING REQUIREMENTS:
- Log bot start/stop events
- Log auto-replies sent (without message content)
- Log configuration loading
- Log errors and exceptions
- Optional debug mode for development

🚀 USAGE FLOW:
1. User runs the script for first time
2. Script prompts for API credentials if not in config
3. Telegram authentication (phone + code)
4. Bot starts monitoring in background
5. User uses Telegram normally
6. When inactive for X minutes, bot auto-replies to new private messages
7. Bot stops auto-replying when user becomes active again

💡 ADDITIONAL FEATURES (OPTIONAL):
- Command to temporarily disable auto-reply
- Statistics tracking (messages replied to)
- Multiple auto-reply messages (random selection)
- Time-based rules (don't reply during certain hours)
- Whitelist/blacklist functionality

🔄 TESTING REQUIREMENTS:
- Test with different inactivity periods
- Test multiple simultaneous conversations
- Test bot behavior when user becomes active
- Test configuration file validation
- Test graceful shutdown and restart

📋 DELIVERABLES:
1. Single Python script (main.py) - fully functional
2. Configuration file template (config.json)
3. Requirements file (requirements.txt)
4. Clear setup and usage instructions

🎯 SUCCESS CRITERIA:
- Bot runs continuously without manual intervention
- Only replies when user is genuinely inactive
- No interference with normal Telegram usage
- Secure and private (no data sharing)
- Easy to configure and customize
- Lightweight and efficient resource usage

⚠️ IMPORTANT NOTES:
- This is a userbot, not a regular bot (runs on user account)
- Requires user's own API credentials from my.telegram.org
- Should comply with Telegram's Terms of Service
- For personal use only, not for commercial purposes
- Test thoroughly before deploying to avoid account issues
