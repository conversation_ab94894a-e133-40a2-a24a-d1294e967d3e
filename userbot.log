2025-07-11 20:00:25,779 - Telegram<PERSON><PERSON>bot - INFO - Starting Telegram Userbot...
2025-07-11 20:00:25,798 - telethon.network.mtprotosender - INFO - Connecting to 149.154.167.51:443/TcpFull...
2025-07-11 20:00:28,555 - telethon.network.mtprotosender - INFO - Connection to 149.154.167.51:443/TcpFull complete!
2025-07-11 20:11:43,920 - TelegramUserbot - INFO - Starting Telegram Userbot...
2025-07-11 20:11:43,922 - telethon.network.mtprotosender - INFO - Connecting to 149.154.167.51:443/TcpFull...
2025-07-11 20:11:44,176 - telethon.network.mtprotosender - INFO - Connection to 149.154.167.51:443/TcpFull complete!
2025-07-11 20:11:45,486 - telethon.client.users - WARNING - Telegram is having internal issues AuthRestartError: Restart the authorization process (caused by SendCodeRequest)
2025-07-11 20:12:09,732 - TelegramUserbot - INFO - Successfully connected as كرار حيدر جواد عبد/م3 (@BBDKB)
2025-07-11 20:12:09,733 - TelegramUserbot - INFO - Userbot is now running and monitoring messages...
2025-07-11 20:13:42,288 - TelegramUserbot - INFO - Starting Telegram Userbot...
2025-07-11 20:13:42,290 - telethon.network.mtprotosender - INFO - Connecting to 149.154.167.51:443/TcpFull...
2025-07-11 20:13:42,406 - telethon.network.mtprotosender - INFO - Connection to 149.154.167.51:443/TcpFull complete!
2025-07-11 20:13:46,551 - TelegramUserbot - INFO - Successfully connected as كرار حيدر جواد عبد/م3 (@BBDKB)
2025-07-11 20:13:46,561 - TelegramUserbot - INFO - Userbot is now running and monitoring messages...
2025-07-11 20:15:32,664 - TelegramUserbot - INFO - Starting Telegram Userbot...
2025-07-11 20:15:32,667 - telethon.network.mtprotosender - INFO - Connecting to 149.154.167.51:443/TcpFull...
2025-07-11 20:15:32,667 - telethon.network.mtprotosender - DEBUG - Connection attempt 1...
2025-07-11 20:15:32,947 - telethon.network.mtprotosender - DEBUG - Connection success!
2025-07-11 20:15:32,947 - telethon.network.mtprotosender - DEBUG - Starting send loop
2025-07-11 20:15:32,950 - telethon.network.mtprotosender - DEBUG - Starting receive loop
2025-07-11 20:15:32,950 - telethon.network.mtprotosender - INFO - Connection to 149.154.167.51:443/TcpFull complete!
2025-07-11 20:15:32,955 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:32,956 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874195045360480 to InvokeWithLayerRequest (1a6b05fd160)
2025-07-11 20:15:32,956 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 76 bytes for sending
2025-07-11 20:15:32,958 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:32,958 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:32,959 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:33,118 - telethon.network.mtprotosender - DEBUG - Handling bad salt for message 7525874195045360480
2025-07-11 20:15:33,118 - telethon.network.mtprotosender - DEBUG - 1 message(s) will be resent
2025-07-11 20:15:33,119 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:33,119 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874195994993720 to InvokeWithLayerRequest (1a6b05fd160)
2025-07-11 20:15:33,120 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 76 bytes for sending
2025-07-11 20:15:33,121 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:33,122 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:33,122 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874196006203208 to MsgsAck (1a6b05fd550)
2025-07-11 20:15:33,123 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:33,124 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:33,124 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:33,343 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:15:33,343 - telethon.network.mtprotosender - DEBUG - Handling new session created
2025-07-11 20:15:33,344 - telethon.network.mtprotosender - DEBUG - Handling acknowledge for [7525874195994993720]
2025-07-11 20:15:33,344 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:33,396 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874195994993720
2025-07-11 20:15:33,398 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:33,399 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874197114327944 to GetUsersRequest (1a6b05fd940)
2025-07-11 20:15:33,400 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-07-11 20:15:33,401 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:33,401 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:33,402 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874197124003920 to MsgsAck (1a6b0604410)
2025-07-11 20:15:33,402 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 60 bytes for sending
2025-07-11 20:15:33,403 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:33,404 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:33,601 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874197114327944
2025-07-11 20:15:33,602 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:33,605 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874197937686476 to GetStateRequest (1a6b05fd940)
2025-07-11 20:15:33,605 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 20 bytes for sending
2025-07-11 20:15:33,607 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:33,607 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:33,608 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874197947948968 to MsgsAck (1a6b060c180)
2025-07-11 20:15:33,608 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:33,609 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:33,610 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:34,085 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:15:34,085 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874197937686476
2025-07-11 20:15:34,086 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:34,087 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:34,088 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874200163068928 to GetDifferenceRequest (1a6b05fd940)
2025-07-11 20:15:34,088 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:34,090 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:34,090 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:34,091 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874200175265468 to MsgsAck (1a6b060c2b0)
2025-07-11 20:15:34,091 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 52 bytes for sending
2025-07-11 20:15:34,093 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:34,094 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:34,483 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874200163068928
2025-07-11 20:15:34,484 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:34,486 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874201754836240 to GetUsersRequest (1a6b0604a50)
2025-07-11 20:15:34,486 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-07-11 20:15:34,489 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:34,489 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:34,490 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874201773372808 to MsgsAck (1a6af252570)
2025-07-11 20:15:34,492 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:34,494 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:34,495 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:34,728 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874201754836240
2025-07-11 20:15:34,734 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:34,736 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874202754861032 to GetUsersRequest (1a6b0604a50)
2025-07-11 20:15:34,737 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-07-11 20:15:34,741 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:34,741 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:34,742 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874202782357372 to MsgsAck (1a6af02b680)
2025-07-11 20:15:34,745 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:34,752 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:34,755 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:35,203 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874202754861032
2025-07-11 20:15:35,204 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:35,205 - TelegramUserbot - INFO - Successfully connected as كرار حيدر جواد عبد/م3 (@BBDKB)
2025-07-11 20:15:35,206 - TelegramUserbot - INFO - Userbot is now running and monitoring messages...
2025-07-11 20:15:35,208 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874204937394896 to GetStateRequest (1a6b0604550)
2025-07-11 20:15:35,208 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 20 bytes for sending
2025-07-11 20:15:35,212 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:35,216 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:35,217 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874204975690644 to MsgsAck (1a6b05c8270)
2025-07-11 20:15:35,218 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:35,223 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:35,223 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:35,410 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:15:35,411 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:35,419 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874205783237256 to GetMessagesRequest (1a6b05fdd30)
2025-07-11 20:15:35,420 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:35,423 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:35,424 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:35,425 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874205805745880 to MsgsAck (1a6af1bba50)
2025-07-11 20:15:35,425 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:35,431 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:35,432 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:35,551 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874204937394896
2025-07-11 20:15:35,552 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:35,688 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874205783237256
2025-07-11 20:15:35,690 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:35,692 - TelegramUserbot - INFO - Received private message from main (ID: 7859654594)
2025-07-11 20:15:35,693 - TelegramUserbot - INFO - User inactive for 60.1 minutes. Should reply: True
2025-07-11 20:15:35,705 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874206927299300 to SendMessageRequest (1a6b05fde80)
2025-07-11 20:15:35,707 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 168 bytes for sending
2025-07-11 20:15:35,714 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:35,716 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:35,717 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874206976179876 to MsgsAck (1a6b05c1b50)
2025-07-11 20:15:35,719 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 44 bytes for sending
2025-07-11 20:15:35,723 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:35,724 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:36,156 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874206927299300
2025-07-11 20:15:36,157 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:36,159 - TelegramUserbot - INFO - Auto-replied to main (ID: 7859654594)
2025-07-11 20:15:36,498 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:36,500 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:38,537 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:38,538 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:38,549 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:15:38,549 - telethon.network.mtprotosender - DEBUG - Handling gzipped data
2025-07-11 20:15:38,550 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:38,550 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:38,551 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:38,552 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:15:38,552 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:15:42,855 - telethon.network.mtprotosender - DEBUG - Handling gzipped data
2025-07-11 20:15:42,858 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:42,859 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:45,825 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:45,829 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:51,328 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:51,329 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:01,295 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:16:01,295 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:16:01,295 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:16:01,296 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:01,296 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:16:02,533 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:16:02,533 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:16:02,534 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:16:02,534 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:02,535 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:16:03,278 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:16:03,279 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:03,286 - telethon.network.mtprotosender - DEBUG - Handling gzipped data
2025-07-11 20:16:03,286 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:16:03,287 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:03,287 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:16:03,288 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:16:04,452 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:16:04,453 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:04,454 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874330477094636 to GetMessagesRequest (1a6b0605e50)
2025-07-11 20:16:04,456 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:16:04,457 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:16:04,458 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:16:04,458 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874330493280396 to MsgsAck (1a6b05f96d0)
2025-07-11 20:16:04,459 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 172 bytes for sending
2025-07-11 20:16:04,461 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:16:04,461 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:16:04,743 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874330477094636
2025-07-11 20:16:04,744 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:04,744 - TelegramUserbot - INFO - Received private message from ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:16:04,745 - TelegramUserbot - INFO - User inactive for 60.5 minutes. Should reply: True
2025-07-11 20:16:04,745 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874331643219932 to SendMessageRequest (1a6b0605d10)
2025-07-11 20:16:04,746 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 168 bytes for sending
2025-07-11 20:16:04,748 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:16:04,749 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:16:04,749 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874331657478316 to MsgsAck (1a6b05f9c70)
2025-07-11 20:16:04,750 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:16:04,751 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:16:04,751 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:16:05,092 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:16:05,092 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874331643219932
2025-07-11 20:16:05,093 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:16:05,093 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:05,094 - TelegramUserbot - INFO - Auto-replied to ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:16:05,094 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:16:05,272 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:16:05,272 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:12,365 - TelegramUserbot - INFO - Starting Telegram Userbot...
2025-07-11 20:26:12,366 - telethon.network.mtprotosender - INFO - Connecting to 149.154.167.51:443/TcpFull...
2025-07-11 20:26:12,367 - telethon.network.mtprotosender - DEBUG - Connection attempt 1...
2025-07-11 20:26:12,583 - telethon.network.mtprotosender - DEBUG - Connection success!
2025-07-11 20:26:12,583 - telethon.network.mtprotosender - DEBUG - Starting send loop
2025-07-11 20:26:12,584 - telethon.network.mtprotosender - DEBUG - Starting receive loop
2025-07-11 20:26:12,585 - telethon.network.mtprotosender - INFO - Connection to 149.154.167.51:443/TcpFull complete!
2025-07-11 20:26:12,595 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:12,598 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876942392985752 to InvokeWithLayerRequest (29f2eb95160)
2025-07-11 20:26:12,599 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 76 bytes for sending
2025-07-11 20:26:12,605 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:12,605 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:12,607 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:12,811 - telethon.network.mtprotostate - DEBUG - Updated time offset (old offset 0, bad 7525876943245204380, good 7525876944393230337, new 1)
2025-07-11 20:26:12,812 - telethon.network.mtprotosender - DEBUG - Handling bad salt for message 7525876942392985752
2025-07-11 20:26:12,813 - telethon.network.mtprotosender - DEBUG - 1 message(s) will be resent
2025-07-11 20:26:12,814 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:12,815 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876947556998304 to InvokeWithLayerRequest (29f2eb95160)
2025-07-11 20:26:12,816 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 76 bytes for sending
2025-07-11 20:26:12,821 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:12,822 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:12,824 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876947591520360 to MsgsAck (29f2eb95550)
2025-07-11 20:26:12,825 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:26:12,828 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:12,828 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:12,945 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:26:12,946 - telethon.network.mtprotosender - DEBUG - Handling new session created
2025-07-11 20:26:12,946 - telethon.network.mtprotosender - DEBUG - Handling acknowledge for [7525876947556998304]
2025-07-11 20:26:12,947 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:13,034 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525876947556998304
2025-07-11 20:26:13,035 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:13,035 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876948733158760 to GetUsersRequest (29f2eb95940)
2025-07-11 20:26:13,036 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-07-11 20:26:13,037 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:13,037 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:13,037 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876948740805320 to MsgsAck (29f2eb987d0)
2025-07-11 20:26:13,038 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 60 bytes for sending
2025-07-11 20:26:13,039 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:13,039 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:13,286 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525876948733158760
2025-07-11 20:26:13,286 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:13,289 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876949749376948 to GetStateRequest (29f2eb95940)
2025-07-11 20:26:13,290 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 20 bytes for sending
2025-07-11 20:26:13,291 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:13,291 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:13,291 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876949756993944 to MsgsAck (29f2eba02b0)
2025-07-11 20:26:13,292 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:26:13,293 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:13,294 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:13,545 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525876949749376948
2025-07-11 20:26:13,545 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:13,546 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876950776158028 to GetDifferenceRequest (29f2eb95940)
2025-07-11 20:26:13,547 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:26:13,549 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:13,549 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:13,550 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876950790825540 to MsgsAck (29f2eba0510)
2025-07-11 20:26:13,550 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:26:13,554 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:13,555 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:13,903 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525876950776158028
2025-07-11 20:26:13,904 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:13,904 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876952209666900 to GetUsersRequest (29f2eb98910)
2025-07-11 20:26:13,905 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-07-11 20:26:13,907 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:13,907 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:13,908 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876952222992592 to MsgsAck (29f2d7b6570)
2025-07-11 20:26:13,908 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:26:13,911 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:13,911 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:14,150 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525876952209666900
2025-07-11 20:26:14,151 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:14,152 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876953493041332 to GetUsersRequest (29f2eb98910)
2025-07-11 20:26:14,152 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-07-11 20:26:14,153 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:14,153 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:14,153 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876953500294980 to MsgsAck (29f2d3cc380)
2025-07-11 20:26:14,154 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:26:14,155 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:14,155 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:14,412 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525876953493041332
2025-07-11 20:26:14,412 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:14,413 - TelegramUserbot - INFO - Successfully connected as كرار حيدر جواد عبد/م3 (@BBDKB)
2025-07-11 20:26:14,414 - TelegramUserbot - INFO - Userbot is now running and monitoring messages...
2025-07-11 20:26:14,414 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876954544394788 to GetStateRequest (29f2eb98e10)
2025-07-11 20:26:14,416 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 20 bytes for sending
2025-07-11 20:26:14,418 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:14,419 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:14,420 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876954564682300 to MsgsAck (29f2eb507c0)
2025-07-11 20:26:14,420 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:26:14,422 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:14,422 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:14,692 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525876954544394788
2025-07-11 20:26:14,694 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:19,466 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:26:19,467 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:19,489 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:26:19,489 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:21,392 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:26:21,392 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:21,403 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:26:21,404 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:21,404 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:26:21,409 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:26:21,409 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:26:21,409 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:26:21,410 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:21,410 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:26:21,411 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876984596119596 to GetMessagesRequest (29f2eb96900)
2025-07-11 20:26:21,412 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:26:21,412 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:21,413 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:21,413 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876984603256892 to MsgsAck (29f2eb55b50)
2025-07-11 20:26:21,413 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 92 bytes for sending
2025-07-11 20:26:21,415 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:21,415 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:21,651 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525876984596119596
2025-07-11 20:26:21,653 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:21,655 - TelegramUserbot - INFO - Received private message from ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:26:21,656 - TelegramUserbot - INFO - User inactive for 60.2 minutes. Should reply: True
2025-07-11 20:26:21,660 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876985592326832 to SendMessageRequest (29f2eb96e40)
2025-07-11 20:26:21,661 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 168 bytes for sending
2025-07-11 20:26:21,666 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:21,666 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:21,667 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525876985617890072 to MsgsAck (29f2eb55e50)
2025-07-11 20:26:21,667 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:26:21,669 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:21,669 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:21,921 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525876985592326832
2025-07-11 20:26:21,921 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:21,922 - TelegramUserbot - INFO - Sent smart reply #1 to ™ StarBoy™ (ID: 6387578933) - Message count: 1
2025-07-11 20:26:22,183 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:26:22,183 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:24,480 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:26:24,481 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:25,872 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:26:25,873 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:25,874 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877003628242692 to GetMessagesRequest (29f2eb98f50)
2025-07-11 20:26:25,875 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:26:25,876 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:25,877 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:25,877 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877003640179832 to MsgsAck (29f2eb8d8b0)
2025-07-11 20:26:25,878 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 60 bytes for sending
2025-07-11 20:26:25,882 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:25,882 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:26,128 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525877003628242692
2025-07-11 20:26:26,130 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:26,132 - TelegramUserbot - INFO - Received private message from ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:26:26,134 - TelegramUserbot - DEBUG - Rate limit active for user 6387578933
2025-07-11 20:26:26,135 - TelegramUserbot - INFO - Not replying to ™ StarBoy™ - conditions not met
2025-07-11 20:26:36,919 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:26:36,920 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:38,612 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:26:38,613 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:38,614 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877058422025076 to GetMessagesRequest (29f2eb99bd0)
2025-07-11 20:26:38,614 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:26:38,615 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:38,616 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:38,616 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877058429302564 to MsgsAck (29f2eb8d7c0)
2025-07-11 20:26:38,616 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 52 bytes for sending
2025-07-11 20:26:38,617 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:38,618 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:38,807 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525877058422025076
2025-07-11 20:26:38,808 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:38,810 - TelegramUserbot - INFO - Received private message from ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:26:38,811 - TelegramUserbot - DEBUG - Rate limit active for user 6387578933
2025-07-11 20:26:38,812 - TelegramUserbot - INFO - Not replying to ™ StarBoy™ - conditions not met
2025-07-11 20:26:40,318 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:26:40,318 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:41,719 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:26:41,719 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:41,721 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877071732988592 to GetMessagesRequest (29f2eba0e90)
2025-07-11 20:26:41,721 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:26:41,723 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:41,723 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:41,724 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877071745481728 to MsgsAck (29f2eb90590)
2025-07-11 20:26:41,724 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 52 bytes for sending
2025-07-11 20:26:41,727 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:41,728 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:42,092 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525877071732988592
2025-07-11 20:26:42,094 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:42,095 - TelegramUserbot - INFO - Received private message from ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:26:42,096 - TelegramUserbot - DEBUG - Rate limit active for user 6387578933
2025-07-11 20:26:42,097 - TelegramUserbot - INFO - Not replying to ™ StarBoy™ - conditions not met
2025-07-11 20:26:49,749 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:26:49,750 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:50,210 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:26:50,210 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:50,211 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877108350424664 to GetMessagesRequest (29f2eba0b00)
2025-07-11 20:26:50,212 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:26:50,213 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:50,214 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:50,214 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877108360844512 to MsgsAck (29f2eb91b70)
2025-07-11 20:26:50,214 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 52 bytes for sending
2025-07-11 20:26:50,215 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:26:50,215 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:26:50,541 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525877108350424664
2025-07-11 20:26:50,543 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:50,545 - TelegramUserbot - INFO - Received private message from ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:26:50,546 - TelegramUserbot - DEBUG - Rate limit active for user 6387578933
2025-07-11 20:26:50,546 - TelegramUserbot - INFO - Not replying to ™ StarBoy™ - conditions not met
2025-07-11 20:26:52,303 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:26:52,304 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:26:58,690 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:26:58,691 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:27:09,780 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:27:09,781 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:27:10,014 - telethon.network.mtprotosender - DEBUG - Handling gzipped data
2025-07-11 20:27:10,015 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:27:10,015 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:27:10,016 - TelegramUserbot - INFO - Received private message from ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:27:10,016 - TelegramUserbot - DEBUG - Rate limit active for user 6387578933
2025-07-11 20:27:10,017 - TelegramUserbot - INFO - Not replying to ™ StarBoy™ - conditions not met
2025-07-11 20:27:13,926 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877209992189716 to PingRequest (29f2eb95550)
2025-07-11 20:27:13,926 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 28 bytes for sending
2025-07-11 20:27:13,928 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:27:13,928 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:27:13,929 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877210004066776 to MsgsAck (29f2d756d00)
2025-07-11 20:27:13,929 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 68 bytes for sending
2025-07-11 20:27:13,930 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:27:13,931 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:27:14,075 - telethon.network.mtprotosender - DEBUG - Handling pong for message 7525877209992189716
2025-07-11 20:27:14,075 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:27:59,578 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:27:59,579 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:27:59,583 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:27:59,584 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:27:59,591 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:27:59,591 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:27:59,592 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:27:59,593 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:27:59,593 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:27:59,603 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:27:59,604 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:27:59,604 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:27:59,605 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:27:59,615 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:27:59,615 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:27:59,615 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:27:59,616 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:27:59,618 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:27:59,619 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:27:59,619 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:27:59,620 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:27:59,621 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:27:59,622 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:27:59,622 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:27:59,622 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:27:59,623 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877406350285704 to GetUsersRequest (29f2eba0c30)
2025-07-11 20:27:59,623 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-07-11 20:27:59,624 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:27:59,625 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:27:59,625 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877406360223944 to MsgsAck (29f2d657b90)
2025-07-11 20:27:59,626 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 140 bytes for sending
2025-07-11 20:27:59,627 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:27:59,627 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:27:59,986 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525877406350285704
2025-07-11 20:27:59,988 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:27:59,992 - TelegramUserbot - INFO - Received private message from ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:27:59,993 - TelegramUserbot - INFO - User inactive for 61.8 minutes. Should reply: True
2025-07-11 20:27:59,996 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877407840813812 to SendMessageRequest (29f2eb99810)
2025-07-11 20:28:00,001 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 168 bytes for sending
2025-07-11 20:28:00,012 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:28:00,027 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:28:00,028 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877408264741716 to MsgsAck (29f2d657ad0)
2025-07-11 20:28:00,028 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:28:00,038 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:28:00,039 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:28:00,931 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525877407840813812
2025-07-11 20:28:00,932 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:28:00,934 - TelegramUserbot - INFO - Sent smart reply #2 to ™ StarBoy™ (ID: 6387578933) - Message count: 2
2025-07-11 20:28:07,821 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:28:07,822 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:28:07,822 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:28:07,823 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:28:07,826 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:28:12,449 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:28:12,450 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:28:13,944 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877467763145412 to PingRequest (29f2eb99810)
2025-07-11 20:28:13,944 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 28 bytes for sending
2025-07-11 20:28:13,946 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:28:13,946 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:28:13,946 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877467773241964 to MsgsAck (29f2eb88aa0)
2025-07-11 20:28:13,947 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 68 bytes for sending
2025-07-11 20:28:13,949 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:28:13,950 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:28:14,428 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:28:14,429 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:28:14,431 - telethon.network.mtprotosender - DEBUG - Handling pong for message 7525877467763145412
2025-07-11 20:28:14,432 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:28:14,433 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877470015559760 to GetUsersRequest (29f2eba0c30)
2025-07-11 20:28:14,435 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-07-11 20:28:14,440 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:28:14,441 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:28:14,441 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877470047695724 to MsgsAck (29f2eb88c00)
2025-07-11 20:28:14,442 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 44 bytes for sending
2025-07-11 20:28:14,445 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:28:14,445 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:28:14,696 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525877470015559760
2025-07-11 20:28:14,697 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:28:14,701 - TelegramUserbot - INFO - Received private message from ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:28:14,703 - TelegramUserbot - DEBUG - Rate limit active for user 6387578933
2025-07-11 20:28:14,703 - TelegramUserbot - INFO - Not replying to ™ StarBoy™ - conditions not met
2025-07-11 20:28:17,884 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:28:17,884 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:28:17,884 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:28:17,885 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:28:19,384 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:28:19,385 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:28:19,389 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877491312775352 to GetUsersRequest (29f2d7b6690)
2025-07-11 20:28:19,392 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-07-11 20:28:19,399 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:28:19,401 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:28:19,402 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877491365366676 to MsgsAck (29f2eb83e30)
2025-07-11 20:28:19,403 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 68 bytes for sending
2025-07-11 20:28:19,408 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:28:19,415 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:28:19,590 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525877491312775352
2025-07-11 20:28:19,591 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:28:19,592 - TelegramUserbot - INFO - Received private message from ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:28:19,593 - TelegramUserbot - DEBUG - Rate limit active for user 6387578933
2025-07-11 20:28:19,593 - TelegramUserbot - INFO - Not replying to ™ StarBoy™ - conditions not met
2025-07-11 20:28:36,611 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:28:36,611 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:28:36,612 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:28:36,612 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:28:36,613 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:28:36,614 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:28:54,341 - telethon.network.mtprotosender - DEBUG - Handling gzipped data
2025-07-11 20:28:54,342 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:28:54,343 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:28:54,349 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:28:54,349 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:28:54,350 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:28:54,351 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:28:54,352 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:28:54,353 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:28:54,353 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:29:01,943 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:29:01,944 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:01,944 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:01,945 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:01,945 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:01,946 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:29:01,947 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:29:02,707 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:29:02,708 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:02,710 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877677279597004 to GetUsersRequest (29f2eb507c0)
2025-07-11 20:29:02,711 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-07-11 20:29:02,714 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:02,714 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:02,715 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877677301571564 to MsgsAck (29f2eb839d0)
2025-07-11 20:29:02,716 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 140 bytes for sending
2025-07-11 20:29:02,718 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:02,719 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:02,866 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525877677279597004
2025-07-11 20:29:02,867 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:02,868 - TelegramUserbot - INFO - Received private message from ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:29:02,868 - TelegramUserbot - INFO - User inactive for 62.8 minutes. Should reply: True
2025-07-11 20:29:02,870 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877677919800480 to SendMessageRequest (29f2eb991d0)
2025-07-11 20:29:02,870 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 400 bytes for sending
2025-07-11 20:29:02,874 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:02,875 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:02,876 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877677943502148 to MsgsAck (29f2d78f530)
2025-07-11 20:29:02,877 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:29:02,880 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:02,880 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:03,106 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525877677919800480
2025-07-11 20:29:03,107 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:03,107 - TelegramUserbot - INFO - Sent commands list to ™ StarBoy™ (ID: 6387578933) after 3 messages
2025-07-11 20:29:03,411 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:03,412 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:10,772 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:10,773 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:11,389 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:11,390 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:13,959 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877725522245032 to PingRequest (29f2eb987d0)
2025-07-11 20:29:13,960 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 28 bytes for sending
2025-07-11 20:29:13,961 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:13,961 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:13,962 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877725533042532 to MsgsAck (29f2eb7e950)
2025-07-11 20:29:13,962 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 60 bytes for sending
2025-07-11 20:29:13,965 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:13,965 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:14,237 - telethon.network.mtprotosender - DEBUG - Handling pong for message 7525877725522245032
2025-07-11 20:29:14,238 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:14,386 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:14,387 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:20,705 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:20,705 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:20,709 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:20,709 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:20,727 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:20,728 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:20,729 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:29:20,730 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:29:21,296 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:21,297 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:22,470 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:22,471 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:22,933 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:29:22,933 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:22,934 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:22,934 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:22,935 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:29:26,352 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:29:26,352 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:26,359 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:26,359 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:26,361 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877778962775004 to GetUsersRequest (29f2d65fdf0)
2025-07-11 20:29:26,362 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 48 bytes for sending
2025-07-11 20:29:26,363 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:26,364 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:26,364 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877778975846064 to MsgsAck (29f2eb7e7d0)
2025-07-11 20:29:26,364 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 124 bytes for sending
2025-07-11 20:29:26,379 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:26,379 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:26,380 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:26,380 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:26,380 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877779042408716 to MsgsAck (29f2eb67000)
2025-07-11 20:29:26,381 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:29:26,396 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:26,396 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:26,397 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:26,397 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:26,398 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877779110533488 to MsgsAck (29f2eb66eb0)
2025-07-11 20:29:26,398 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:29:26,399 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:29:26,399 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:29:26,399 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:29:26,400 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:26,400 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:26,691 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525877778962775004
2025-07-11 20:29:26,692 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:26,693 - TelegramUserbot - INFO - Received private message from ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:29:26,694 - TelegramUserbot - DEBUG - Rate limit active for user 6387578933
2025-07-11 20:29:26,695 - TelegramUserbot - INFO - Not replying to ™ StarBoy™ - conditions not met
2025-07-11 20:29:26,697 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:26,697 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:29,953 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:29,954 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:34,253 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:34,254 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:36,531 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:29:36,532 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:36,533 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:36,533 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:36,534 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:29:37,530 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:37,530 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:38,616 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:29:38,617 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:38,618 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877831533292096 to GetMessagesRequest (29f2d7b68d0)
2025-07-11 20:29:38,619 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:29:38,620 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:38,621 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:38,621 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877831544503492 to MsgsAck (29f2eb4b7d0)
2025-07-11 20:29:38,622 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 100 bytes for sending
2025-07-11 20:29:38,624 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:38,624 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:39,011 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525877831533292096
2025-07-11 20:29:39,013 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:39,014 - TelegramUserbot - INFO - Received private message from main (ID: 7859654594)
2025-07-11 20:29:39,015 - TelegramUserbot - INFO - User inactive for 63.4 minutes. Should reply: True
2025-07-11 20:29:39,016 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877833419487876 to SendMessageRequest (29f2eba0e90)
2025-07-11 20:29:39,017 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 168 bytes for sending
2025-07-11 20:29:39,021 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:39,022 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:39,022 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877833442675512 to MsgsAck (29f2eb4bf50)
2025-07-11 20:29:39,023 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:29:39,025 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:29:39,025 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:29:39,488 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525877833419487876
2025-07-11 20:29:39,489 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:39,490 - TelegramUserbot - INFO - Sent smart reply #1 to main (ID: 7859654594) - Message count: 1
2025-07-11 20:29:40,601 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:40,602 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:49,724 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:49,725 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:51,159 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:51,160 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:51,178 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:51,178 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:52,411 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:52,411 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:54,789 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:29:54,790 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:29:54,790 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:29:54,791 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:29:54,793 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:30:13,979 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877983298531772 to PingRequest (29f2eba0770)
2025-07-11 20:30:13,980 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 28 bytes for sending
2025-07-11 20:30:13,982 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:30:13,982 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:30:13,982 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525877983313066720 to MsgsAck (29f2eba7d40)
2025-07-11 20:30:13,983 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 100 bytes for sending
2025-07-11 20:30:13,984 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:30:13,984 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:30:14,557 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:30:14,558 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:30:14,571 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:30:14,572 - telethon.network.mtprotosender - DEBUG - Handling pong for message 7525877983298531772
2025-07-11 20:30:14,572 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:30:14,573 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:30:14,574 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:30:14,590 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:30:14,591 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:30:14,591 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:30:14,592 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:30:14,593 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:30:14,593 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:13,988 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525878241033382312 to PingRequest (29f2eba0e90)
2025-07-11 20:31:13,988 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 28 bytes for sending
2025-07-11 20:31:13,990 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:31:13,990 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:31:13,991 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525878241044390576 to MsgsAck (29f2ebac4b0)
2025-07-11 20:31:13,991 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 76 bytes for sending
2025-07-11 20:31:13,993 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:31:13,993 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:31:14,305 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:14,306 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:14,321 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:31:14,321 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:14,322 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:14,322 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:14,331 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:31:14,333 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:14,333 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:14,333 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:14,337 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:31:14,338 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:14,338 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:14,338 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:14,349 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:31:14,349 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:14,350 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:14,350 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:14,354 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:31:14,354 - telethon.network.mtprotosender - DEBUG - Handling pong for message 7525878241033382312
2025-07-11 20:31:14,355 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:14,355 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:14,355 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:14,374 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:14,374 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:14,375 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:14,375 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:14,376 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:14,376 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:14,379 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:14,380 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:14,382 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:14,382 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:14,382 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:14,383 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:14,383 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:14,384 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:34,836 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:34,837 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:34,850 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:31:34,851 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:34,851 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:31:34,852 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:34,852 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:34,864 - telethon.network.mtprotosender - DEBUG - Handling gzipped data
2025-07-11 20:31:34,866 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:34,866 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:35,359 - telethon.client.updates - DEBUG - Timeout waiting for updates expired
2025-07-11 20:31:51,532 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:31:51,533 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:31:51,534 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:51,536 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:51,545 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:51,547 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:51,558 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:31:51,559 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:51,559 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:31:51,560 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:51,563 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:51,564 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:51,564 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:31:53,070 - telethon.network.mtprotosender - DEBUG - Handling gzipped data
2025-07-11 20:31:53,071 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:31:53,071 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:53,072 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525878409169302928 to GetMessagesRequest (29f2eb50d10)
2025-07-11 20:31:53,073 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:31:53,074 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:31:53,075 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:31:53,075 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525878409180483804 to MsgsAck (29f2ebad180)
2025-07-11 20:31:53,075 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 276 bytes for sending
2025-07-11 20:31:53,086 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:31:53,087 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:31:53,210 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525878409169302928
2025-07-11 20:31:53,213 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:53,217 - TelegramUserbot - INFO - Received private message from Telegram (ID: 777000)
2025-07-11 20:31:53,217 - TelegramUserbot - INFO - User inactive for 65.7 minutes. Should reply: True
2025-07-11 20:31:53,218 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525878409751601208 to SendMessageRequest (29f2eba0180)
2025-07-11 20:31:53,224 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 168 bytes for sending
2025-07-11 20:31:53,226 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:31:53,226 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:31:53,228 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525878409790747628 to MsgsAck (29f2ebad680)
2025-07-11 20:31:53,228 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:31:53,231 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:31:53,232 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:31:53,446 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525878409751601208
2025-07-11 20:31:53,452 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:31:53,453 - TelegramUserbot - INFO - Sent smart reply #1 to Telegram (ID: 777000) - Message count: 1
2025-07-11 20:32:00,508 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:32:00,510 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:32:00,510 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:32:00,512 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:32:00,513 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:32:00,515 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:32:00,519 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:32:01,520 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:32:01,521 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:32:02,072 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:32:02,072 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:32:02,998 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:32:02,998 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:32:03,240 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:32:03,241 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:32:03,243 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525878452801921140 to GetMessagesRequest (29f2eb509e0)
2025-07-11 20:32:03,244 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:32:03,246 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:32:03,247 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:32:03,248 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525878452821197760 to MsgsAck (29f2ebad6d0)
2025-07-11 20:32:03,249 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 100 bytes for sending
2025-07-11 20:32:03,256 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:32:03,257 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:32:03,594 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525878452801921140
2025-07-11 20:32:03,596 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:32:03,597 - TelegramUserbot - INFO - Received private message from main (ID: 7859654594)
2025-07-11 20:32:03,598 - TelegramUserbot - INFO - User inactive for 65.9 minutes. Should reply: True
2025-07-11 20:32:03,601 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525878454234557400 to SendMessageRequest (29f2d7b6570)
2025-07-11 20:32:03,606 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 168 bytes for sending
2025-07-11 20:32:03,615 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:32:03,616 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:32:03,622 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525878454317432652 to MsgsAck (29f2ebac140)
2025-07-11 20:32:03,623 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:32:03,626 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:32:03,626 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:32:04,105 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525878454234557400
2025-07-11 20:32:04,106 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:32:04,107 - TelegramUserbot - INFO - Sent smart reply #2 to main (ID: 7859654594) - Message count: 2
2025-07-11 20:32:04,394 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:32:04,395 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:32:05,778 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:32:05,778 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:32:06,815 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:32:06,819 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:32:06,902 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:32:06,905 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
