#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verification Handler for Hugging Face Spaces
============================================
Handles Telegram verification codes in non-interactive environments
"""

import os
import json
import asyncio
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional

try:
    from telethon import TelegramClient
    from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError
except ImportError:
    print("❌ Error: Telethon library not found!")
    print("📦 Please install it with: pip install telethon")
    exit(1)


class VerificationHandler:
    def __init__(self, config: dict):
        """Initialize verification handler."""
        self.config = config
        self.logger = logging.getLogger('VerificationHandler')
        self.verification_file = Path('verification_data.json')
        self.session_backup_file = Path('session_backup.session')
        
    def save_verification_state(self, phone_code_hash: str, phone_number: str):
        """Save verification state for later use."""
        verification_data = {
            'phone_code_hash': phone_code_hash,
            'phone_number': phone_number,
            'timestamp': datetime.now().isoformat(),
            'status': 'waiting_for_code'
        }
        
        with open(self.verification_file, 'w', encoding='utf-8') as f:
            json.dump(verification_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info("Verification state saved. Waiting for code input via web interface.")
    
    def load_verification_state(self) -> Optional[dict]:
        """Load verification state."""
        if not self.verification_file.exists():
            return None
            
        try:
            with open(self.verification_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Check if data is not too old (30 minutes max)
            timestamp = datetime.fromisoformat(data['timestamp'])
            if datetime.now() - timestamp > timedelta(minutes=30):
                self.logger.warning("Verification data is too old, removing...")
                self.verification_file.unlink()
                return None
                
            return data
        except Exception as e:
            self.logger.error(f"Error loading verification state: {e}")
            return None
    
    def get_pending_verification_code(self) -> Optional[str]:
        """Check if there's a pending verification code."""
        code_file = Path('verification_code.txt')
        if code_file.exists():
            try:
                with open(code_file, 'r', encoding='utf-8') as f:
                    code = f.read().strip()
                
                # Remove the file after reading
                code_file.unlink()
                return code
            except Exception as e:
                self.logger.error(f"Error reading verification code: {e}")
                return None
        return None
    
    def save_verification_code(self, code: str):
        """Save verification code from web interface."""
        code_file = Path('verification_code.txt')
        with open(code_file, 'w', encoding='utf-8') as f:
            f.write(code.strip())
        self.logger.info("Verification code saved from web interface")
    
    def clear_verification_state(self):
        """Clear all verification-related files."""
        files_to_remove = [
            self.verification_file,
            Path('verification_code.txt'),
            Path('needs_verification.flag')
        ]
        
        for file_path in files_to_remove:
            if file_path.exists():
                try:
                    file_path.unlink()
                    self.logger.debug(f"Removed {file_path}")
                except Exception as e:
                    self.logger.error(f"Error removing {file_path}: {e}")
    
    async def handle_verification_process(self, client: TelegramClient) -> bool:
        """Handle the complete verification process."""
        try:
            # Check if we have a saved verification state
            verification_data = self.load_verification_state()
            
            if verification_data and verification_data.get('status') == 'waiting_for_code':
                # We're in the middle of verification process
                phone_code_hash = verification_data['phone_code_hash']
                phone_number = verification_data['phone_number']
                
                # Check for pending verification code
                code = self.get_pending_verification_code()
                if code:
                    try:
                        await client.sign_in(phone_number, code, phone_code_hash=phone_code_hash)
                        self.logger.info("Successfully signed in with verification code!")
                        self.clear_verification_state()
                        return True
                    except PhoneCodeInvalidError:
                        self.logger.error("Invalid verification code provided")
                        return False
                    except SessionPasswordNeededError:
                        self.logger.error("Two-factor authentication required (not supported in Spaces)")
                        return False
                else:
                    self.logger.info("Waiting for verification code via web interface...")
                    return False
            else:
                # Start new verification process
                try:
                    await client.connect()
                    if await client.is_user_authorized():
                        self.logger.info("Already authorized!")
                        return True
                    
                    # Send code request
                    phone_number = self.config['phone_number']
                    sent_code = await client.send_code_request(phone_number)
                    
                    # Save verification state
                    self.save_verification_state(sent_code.phone_code_hash, phone_number)
                    
                    self.logger.info(f"Verification code sent to {phone_number}")
                    self.logger.info("Please enter the code via the web interface")
                    
                    return False
                    
                except Exception as e:
                    self.logger.error(f"Error in verification process: {e}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error handling verification: {e}")
            return False
    
    def backup_session(self):
        """Backup the current session file."""
        session_file = Path(f"{self.config['session_name']}.session")
        if session_file.exists():
            try:
                import shutil
                shutil.copy2(session_file, self.session_backup_file)
                self.logger.info("Session backed up successfully")
            except Exception as e:
                self.logger.error(f"Error backing up session: {e}")
    
    def restore_session(self):
        """Restore session from backup."""
        if self.session_backup_file.exists():
            try:
                import shutil
                session_file = Path(f"{self.config['session_name']}.session")
                shutil.copy2(self.session_backup_file, session_file)
                self.logger.info("Session restored from backup")
                return True
            except Exception as e:
                self.logger.error(f"Error restoring session: {e}")
                return False
        return False
    
    def get_verification_status(self) -> dict:
        """Get current verification status for web interface."""
        status = {
            'needs_verification': os.path.exists('needs_verification.flag'),
            'waiting_for_code': False,
            'phone_number': None,
            'timestamp': None
        }
        
        verification_data = self.load_verification_state()
        if verification_data:
            status['waiting_for_code'] = verification_data.get('status') == 'waiting_for_code'
            status['phone_number'] = verification_data.get('phone_number', '').replace(self.config['phone_number'][:-4], '****')
            status['timestamp'] = verification_data.get('timestamp')
        
        return status
