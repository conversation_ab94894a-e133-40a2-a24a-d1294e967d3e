# استخدام Python 3.11 slim كصورة أساسية
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# تعيين مجلد العمل
WORKDIR /app

# تثبيت المتطلبات النظام
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# نسخ ملف المتطلبات وتثبيت المكتبات
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات التطبيق
COPY main.py .
COPY app.py .
COPY verification_handler.py .
COPY config.json .

# إنشاء مجلد للجلسات والسجلات
RUN mkdir -p /app/data

# تعيين الصلاحيات
RUN chmod +x main.py

# إنشاء مستخدم غير جذر لتشغيل التطبيق
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# تعيين متغير البيئة لمجلد البيانات
ENV DATA_DIR=/app/data

# المنفذ لـ Hugging Face Spaces
EXPOSE 7860

# الأمر الافتراضي لتشغيل البوت
# للاستخدام المحلي: python main.py
# لـ Hugging Face Spaces: python app.py
CMD ["python", "app.py"]
