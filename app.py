#!/usr/bin/env python3
"""
Hugging Face Spaces wrapper for Telegram Userbot
This file is required for Hugging Face Spaces compatibility
"""

import os
import subprocess
import sys
import time
import threading
from pathlib import Path

def setup_environment():
    """Setup environment variables from Hugging Face Spaces secrets"""
    # Get secrets from environment variables
    api_id = os.getenv('API_ID')
    api_hash = os.getenv('API_HASH') 
    phone_number = os.getenv('PHONE_NUMBER')
    
    if api_id and api_hash and phone_number:
        # Update config.json with actual values
        import json
        
        config_path = Path('config.json')
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Update with environment variables
            config['api_id'] = api_id
            config['api_hash'] = api_hash
            config['phone_number'] = phone_number
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
            print("✅ Configuration updated from environment variables")
        else:
            print("❌ config.json not found")
    else:
        print("⚠️ Environment variables not set. Please configure API_ID, API_HASH, and PHONE_NUMBER in Spaces secrets.")

def run_userbot():
    """Run the userbot in a separate thread"""
    try:
        print("🚀 Starting Telegram Userbot...")

        # Run the userbot process
        process = subprocess.Popen(
            [sys.executable, 'main.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )

        # Stream output to log file and console
        log_file = Path('userbot.log')
        with open(log_file, 'a', encoding='utf-8') as f:
            for line in process.stdout:
                print(line.strip())
                f.write(line)
                f.flush()

        process.wait()

    except subprocess.CalledProcessError as e:
        print(f"❌ Error running userbot: {e}")
    except KeyboardInterrupt:
        print("🛑 Userbot stopped by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def create_simple_interface():
    """Create a simple web interface for monitoring"""
    try:
        import gradio as gr
        from verification_handler import VerificationHandler
        import json

        def get_status():
            """Get userbot status"""
            log_file = Path('userbot.log')
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    return ''.join(lines[-20:])  # Last 20 lines
            return "No logs available yet..."

        def get_config():
            """Get current configuration (without sensitive data)"""
            config_file = Path('config.json')
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # Hide sensitive information
                safe_config = config.copy()
                safe_config['api_hash'] = '***hidden***'
                safe_config['phone_number'] = '***hidden***'

                return json.dumps(safe_config, ensure_ascii=False, indent=2)
            return "Configuration not found"

        def get_verification_status():
            """Get verification status"""
            try:
                config_file = Path('config.json')
                if config_file.exists():
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                    handler = VerificationHandler(config)
                    status = handler.get_verification_status()

                    if status['needs_verification'] or status['waiting_for_code']:
                        if status['waiting_for_code']:
                            return f"🔐 Waiting for verification code for {status['phone_number']}"
                        else:
                            return "🔐 Verification required - please restart the bot"
                    else:
                        return "✅ Bot is authenticated and running"
                else:
                    return "❌ Configuration not found"
            except Exception as e:
                return f"❌ Error checking verification status: {e}"

        def submit_verification_code(code):
            """Submit verification code"""
            try:
                if not code or not code.strip():
                    return "❌ Please enter a verification code"

                config_file = Path('config.json')
                if config_file.exists():
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                    handler = VerificationHandler(config)
                    handler.save_verification_code(code.strip())

                    return f"✅ Verification code {code} submitted successfully! Please wait for the bot to process it."
                else:
                    return "❌ Configuration not found"
            except Exception as e:
                return f"❌ Error submitting code: {e}"
        
        # Create Gradio interface
        with gr.Blocks(title="Telegram Userbot Monitor", theme=gr.themes.Soft()) as interface:
            gr.Markdown("# 🤖 Telegram Userbot Monitor")
            gr.Markdown("Monitor your Telegram userbot status and logs")

            with gr.Tab("🔐 Verification"):
                gr.Markdown("### Telegram Verification")
                verification_status = gr.Textbox(
                    label="Verification Status",
                    value=get_verification_status(),
                    interactive=False
                )

                with gr.Row():
                    verification_code_input = gr.Textbox(
                        label="Verification Code",
                        placeholder="Enter the 5-digit code from Telegram",
                        max_lines=1
                    )
                    submit_code_btn = gr.Button("📤 Submit Code", variant="primary")

                verification_result = gr.Textbox(
                    label="Result",
                    interactive=False
                )

                refresh_verification_btn = gr.Button("🔄 Refresh Status")

                # Event handlers for verification tab
                submit_code_btn.click(
                    submit_verification_code,
                    inputs=verification_code_input,
                    outputs=verification_result
                )
                refresh_verification_btn.click(
                    get_verification_status,
                    outputs=verification_status
                )

            with gr.Tab("📊 Status"):
                status_output = gr.Textbox(
                    label="Recent Logs",
                    lines=15,
                    max_lines=20,
                    value=get_status()
                )
                refresh_btn = gr.Button("🔄 Refresh Logs")
                refresh_btn.click(get_status, outputs=status_output)

            with gr.Tab("⚙️ Configuration"):
                config_output = gr.Textbox(
                    label="Current Configuration",
                    lines=10,
                    value=get_config()
                )
                refresh_config_btn = gr.Button("🔄 Refresh Config")
                refresh_config_btn.click(get_config, outputs=config_output)
            
            with gr.Tab("📖 Help"):
                gr.Markdown("""
                ## 📖 How to use:

                ### 🚀 First Time Setup:
                1. **Configure Environment Variables** in Hugging Face Spaces:
                   - `API_ID`: Your Telegram API ID
                   - `API_HASH`: Your Telegram API Hash
                   - `PHONE_NUMBER`: Your phone number (with country code, e.g., +1234567890)

                2. **Verification Process**:
                   - Go to the **Verification** tab
                   - The bot will automatically send a verification code to your phone
                   - Enter the 5-digit code in the verification field
                   - Click "Submit Code"

                3. **Monitor Status**:
                   - Check the **Status** tab for recent activity and logs
                   - View current settings in **Configuration** tab

                ### 🔧 Environment Variables Setup:
                1. Go to your Hugging Face Space settings
                2. Add these secrets:
                   - `API_ID`: Get from https://my.telegram.org
                   - `API_HASH`: Get from https://my.telegram.org
                   - `PHONE_NUMBER`: Your phone number with country code

                ### 📝 Bot Features:
                - ✅ Auto-reply when inactive for specified minutes
                - ✅ Session persistence across restarts
                - ✅ Smart varied replies (different each time)
                - ✅ Command system after 3+ messages
                - ✅ Custom responses for specific commands
                - ✅ User exclusion list
                - ✅ Rate limiting to prevent spam

                ### 🔐 Security:
                - Session files are securely stored
                - Sensitive data is hidden in logs
                - Automatic session backup and restore

                ### ❓ Troubleshooting:
                - If verification fails, check the Status tab for error messages
                - Make sure your phone number includes the country code
                - Verification codes expire after 30 minutes
                - If stuck, restart the Space and try again
                """)
        
        return interface
        
    except ImportError:
        print("⚠️ Gradio not available, running in headless mode")
        return None

def main():
    """Main function for Hugging Face Spaces"""
    print("🌟 Telegram Userbot for Hugging Face Spaces")
    print("=" * 50)
    
    # Setup environment
    setup_environment()
    
    # Start userbot in background thread
    userbot_thread = threading.Thread(target=run_userbot, daemon=True)
    userbot_thread.start()
    
    # Create and launch interface
    interface = create_simple_interface()
    
    if interface:
        print("🌐 Starting web interface...")
        interface.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            show_error=True
        )
    else:
        print("📱 Running in headless mode...")
        # Keep the main thread alive
        try:
            while True:
                time.sleep(60)
                print("💓 Userbot is running...")
        except KeyboardInterrupt:
            print("🛑 Shutting down...")

if __name__ == "__main__":
    main()
